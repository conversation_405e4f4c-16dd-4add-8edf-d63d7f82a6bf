<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link active">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/reports') ?>" class="nav-link">
        <span class="nav-icon">📈</span>
        <span class="nav-text">Reports</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<button class="btn btn-secondary" onclick="exportData('csv')">
    Export CSV
</button>
<a href="<?= base_url('dakoii/organizations/create') ?>" class="btn btn-primary">
    Create Organization
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Statistics Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Total</div>
            <div style="font-size: 2rem; font-weight: 700; color: var(--text-primary);"><?= number_format($stats['total']) ?></div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Active</div>
            <div style="font-size: 2rem; font-weight: 700; color: #06FFA5;"><?= number_format($stats['active']) ?></div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Paid</div>
            <div style="font-size: 2rem; font-weight: 700; color: #00D4FF;"><?= number_format($stats['paid']) ?></div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Unpaid</div>
            <div style="font-size: 2rem; font-weight: 700; color: #FF006E;"><?= number_format($stats['unpaid']) ?></div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div class="card-header">Filters</div>
        <form method="GET" action="<?= base_url('dakoii/organizations') ?>" id="filterForm">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div class="form-group">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" id="search" name="search" class="form-input" placeholder="Name, code, email..." value="<?= esc($filters['search']) ?>">
                </div>
                <div class="form-group">
                    <label for="status" class="form-label">Status</label>
                    <select id="status" name="status" class="form-input">
                        <option value="">All Statuses</option>
                        <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="license_status" class="form-label">License</label>
                    <select id="license_status" name="license_status" class="form-input">
                        <option value="">All Licenses</option>
                        <option value="paid" <?= $filters['license_status'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                        <option value="unpaid" <?= $filters['license_status'] === 'unpaid' ? 'selected' : '' ?>>Unpaid</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="per_page" class="form-label">Per Page</label>
                    <select id="per_page" name="per_page" class="form-input">
                        <option value="10" <?= $filters['per_page'] == 10 ? 'selected' : '' ?>>10</option>
                        <option value="25" <?= $filters['per_page'] == 25 ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= $filters['per_page'] == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $filters['per_page'] == 100 ? 'selected' : '' ?>>100</option>
                    </select>
                </div>
            </div>
            <div style="display: flex; gap: var(--spacing-md);">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">Clear</a>
            </div>
        </form>
    </div>

    <!-- Organizations List -->
    <div class="card">
        <div class="card-header">Organizations (<?= $pager->getDetails()['total'] ?> total)</div>
        
        <?php if (empty($organizations)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-tertiary);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🏢</div>
                <div>No organizations found</div>
                <a href="<?= base_url('dakoii/organizations/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-lg);">
                    Create First Organization
                </a>
            </div>
        <?php else: ?>
            <!-- Table View -->
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 1px solid var(--glass-border);">
                            <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-secondary);">
                                <a href="?<?= http_build_query(array_merge($filters, ['sort_field' => 'org_code', 'sort_direction' => $filters['sort_field'] === 'org_code' && $filters['sort_direction'] === 'ASC' ? 'DESC' : 'ASC'])) ?>" style="color: inherit; text-decoration: none;">
                                    Code <?= $filters['sort_field'] === 'org_code' ? ($filters['sort_direction'] === 'ASC' ? '↑' : '↓') : '' ?>
                                </a>
                            </th>
                            <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-secondary);">
                                <a href="?<?= http_build_query(array_merge($filters, ['sort_field' => 'name', 'sort_direction' => $filters['sort_field'] === 'name' && $filters['sort_direction'] === 'ASC' ? 'DESC' : 'ASC'])) ?>" style="color: inherit; text-decoration: none;">
                                    Name <?= $filters['sort_field'] === 'name' ? ($filters['sort_direction'] === 'ASC' ? '↑' : '↓') : '' ?>
                                </a>
                            </th>
                            <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-secondary);">Status</th>
                            <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-secondary);">License</th>
                            <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-secondary);">
                                <a href="?<?= http_build_query(array_merge($filters, ['sort_field' => 'created_at', 'sort_direction' => $filters['sort_field'] === 'created_at' && $filters['sort_direction'] === 'ASC' ? 'DESC' : 'ASC'])) ?>" style="color: inherit; text-decoration: none;">
                                    Created <?= $filters['sort_field'] === 'created_at' ? ($filters['sort_direction'] === 'ASC' ? '↑' : '↓') : '' ?>
                                </a>
                            </th>
                            <th style="padding: var(--spacing-md); text-align: right; font-weight: 600; color: var(--text-secondary);">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($organizations as $org): ?>
                            <tr style="border-bottom: 1px solid var(--glass-border); transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='var(--glass-bg)'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: var(--spacing-md);">
                                    <span style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary);"><?= esc($org['org_code']) ?></span>
                                </td>
                                <td style="padding: var(--spacing-md);">
                                    <div style="display: flex; align-items: center;">
                                        <?php if ($org['logo_path']): ?>
                                            <img src="<?= base_url($org['logo_path']) ?>" alt="Logo" style="width: 32px; height: 32px; border-radius: var(--radius-sm); margin-right: var(--spacing-sm); object-fit: cover;">
                                        <?php else: ?>
                                            <div style="width: 32px; height: 32px; border-radius: var(--radius-sm); background: var(--gradient-primary); margin-right: var(--spacing-sm); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                                                <?= strtoupper(substr($org['name'], 0, 1)) ?>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div style="font-weight: 600; color: var(--text-primary);"><?= esc($org['name']) ?></div>
                                            <?php if ($org['contact_email']): ?>
                                                <div style="font-size: 0.75rem; color: var(--text-tertiary);"><?= esc($org['contact_email']) ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: var(--spacing-md);">
                                    <span class="status-badge status-<?= $org['is_active'] ? 'active' : 'inactive' ?>">
                                        <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td style="padding: var(--spacing-md);">
                                    <span class="status-badge status-<?= $org['license_status'] ?>">
                                        <?= ucfirst($org['license_status']) ?>
                                    </span>
                                </td>
                                <td style="padding: var(--spacing-md); color: var(--text-tertiary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($org['created_at'])) ?>
                                </td>
                                <td style="padding: var(--spacing-md); text-align: right;">
                                    <div style="display: flex; gap: var(--spacing-xs); justify-content: flex-end;">
                                        <a href="<?= base_url('dakoii/organizations/' . $org['id']) ?>" class="btn-icon" title="View">👁️</a>
                                        <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins') ?>" class="btn-icon" title="Admins">👥</a>
                                        <button onclick="toggleStatus(<?= $org['id'] ?>, '<?= $org['is_active'] ? 'deactivate' : 'activate' ?>')" class="btn-icon" title="<?= $org['is_active'] ? 'Deactivate' : 'Activate' ?>">
                                            <?= $org['is_active'] ? '⏸️' : '▶️' ?>
                                        </button>
                                        <button onclick="toggleLicense(<?= $org['id'] ?>, '<?= $org['license_status'] === 'paid' ? 'unpaid' : 'paid' ?>')" class="btn-icon" title="Toggle License">💳</button>
                                        <button onclick="deleteOrganization(<?= $org['id'] ?>, '<?= esc($org['name']) ?>')" class="btn-icon" title="Delete">🗑️</button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div style="padding: var(--spacing-lg); border-top: 1px solid var(--glass-border); display: flex; justify-content: between; align-items: center;">
                <div style="color: var(--text-tertiary); font-size: 0.875rem;">
                    Showing <?= $pager->getDetails()['start'] ?> to <?= $pager->getDetails()['end'] ?> of <?= $pager->getDetails()['total'] ?> results
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(6, 255, 165, 0.1);
    color: #06FFA5;
}

.status-inactive {
    background: rgba(124, 128, 145, 0.1);
    color: var(--text-muted);
}

.status-paid {
    background: rgba(0, 212, 255, 0.1);
    color: #00D4FF;
}

.status-unpaid {
    background: rgba(255, 0, 110, 0.1);
    color: #FF006E;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--glass-bg);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 14px;
}

.btn-icon:hover {
    background: var(--glass-border);
    transform: translateY(-1px);
}
</style>

<script>
function exportData(format) {
    window.open('<?= base_url('dakoii/organizations/export?format=') ?>' + format, '_blank');
}

function toggleStatus(id, action) {
    if (confirm('Are you sure you want to ' + action + ' this organization?')) {
        fetch('<?= base_url('dakoii/organizations/') ?>' + id + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function toggleLicense(id, newStatus) {
    if (confirm('Are you sure you want to change the license status to ' + newStatus + '?')) {
        fetch('<?= base_url('dakoii/organizations/') ?>' + id + '/license-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function deleteOrganization(id, name) {
    if (confirm('Are you sure you want to delete "' + name + '"? This action cannot be undone.')) {
        fetch('<?= base_url('dakoii/organizations/') ?>' + id, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterInputs = document.querySelectorAll('#filterForm select, #filterForm input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.name !== 'search') {
                document.getElementById('filterForm').submit();
            }
        });
    });

    // Debounced search
    let searchTimeout;
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            document.getElementById('filterForm').submit();
        }, 500);
    });
});
</script>
<?= $this->endSection() ?>
