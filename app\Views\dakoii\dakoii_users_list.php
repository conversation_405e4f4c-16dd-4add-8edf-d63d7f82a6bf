<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<ul class="nav-list">
    <li><a href="<?= base_url('dakoii/dashboard') ?>"><i class="icon">📊</i> Dashboard</a></li>
    <li><a href="<?= base_url('dakoii/organizations') ?>"><i class="icon">🏢</i> Organizations</a></li>
    <li class="active"><a href="<?= base_url('dakoii/users') ?>"><i class="icon">👥</i> System Users</a></li>
    <li><a href="<?= base_url('dakoii/government') ?>"><i class="icon">🏛️</i> Government Structure</a></li>
    <li><a href="<?= base_url('dakoii/logout') ?>"><i class="icon">🚪</i> Logout</a></li>
</ul>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<?php if (in_array($current_user['role'], ['admin', 'moderator'])): ?>
<a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-primary">
    <i class="icon">➕</i> Create User
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-content">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card glass-effect">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
                <h3><?= number_format($stats['total_users']) ?></h3>
                <p>Total Users</p>
            </div>
        </div>
        <div class="stat-card glass-effect">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3><?= number_format($stats['active_users']) ?></h3>
                <p>Active Users</p>
            </div>
        </div>
        <div class="stat-card glass-effect">
            <div class="stat-icon">👑</div>
            <div class="stat-content">
                <h3><?= number_format($stats['admin_users']) ?></h3>
                <p>Administrators</p>
            </div>
        </div>
        <div class="stat-card glass-effect">
            <div class="stat-icon">🛡️</div>
            <div class="stat-content">
                <h3><?= number_format($stats['moderator_users']) ?></h3>
                <p>Moderators</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section glass-effect">
        <form method="GET" action="<?= base_url('dakoii/users') ?>" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Search users..." value="<?= esc($search) ?>" class="form-control">
            </div>
            <div class="filter-group">
                <select name="role" class="form-control">
                    <option value="">All Roles</option>
                    <option value="admin" <?= $roleFilter === 'admin' ? 'selected' : '' ?>>Admin</option>
                    <option value="moderator" <?= $roleFilter === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                    <option value="user" <?= $roleFilter === 'user' ? 'selected' : '' ?>>User</option>
                </select>
            </div>
            <div class="filter-group">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" <?= $statusFilter === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $statusFilter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            <div class="filter-group">
                <select name="per_page" class="form-control">
                    <option value="10" <?= $perPage == 10 ? 'selected' : '' ?>>10 per page</option>
                    <option value="25" <?= $perPage == 25 ? 'selected' : '' ?>>25 per page</option>
                    <option value="50" <?= $perPage == 50 ? 'selected' : '' ?>>50 per page</option>
                    <option value="100" <?= $perPage == 100 ? 'selected' : '' ?>>100 per page</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Filter</button>
            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">Clear</a>
        </form>
    </div>

    <!-- Bulk Actions -->
    <?php if ($current_user['role'] === 'admin'): ?>
    <div class="bulk-actions glass-effect" style="display: none;">
        <form method="POST" action="<?= base_url('dakoii/users/bulk-action') ?>" id="bulkForm">
            <?= csrf_field() ?>
            <div class="bulk-controls">
                <select name="bulk_action" class="form-control" required>
                    <option value="">Select Action</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="delete">Delete</option>
                </select>
                <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to perform this bulk action?')">
                    Apply to Selected
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearSelection()">Cancel</button>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <!-- Users List -->
    <div class="users-grid">
        <?php if (empty($users)): ?>
            <div class="empty-state glass-effect">
                <div class="empty-icon">👥</div>
                <h3>No Users Found</h3>
                <p>No users match your current filters.</p>
                <?php if (in_array($current_user['role'], ['admin', 'moderator'])): ?>
                    <a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-primary">Create First User</a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($users as $user): ?>
                <div class="user-card glass-effect">
                    <div class="user-header">
                        <?php if ($current_user['role'] === 'admin'): ?>
                            <input type="checkbox" name="user_ids[]" value="<?= $user['id'] ?>" class="user-checkbox" onchange="toggleBulkActions()">
                        <?php endif; ?>
                        <div class="user-avatar">
                            <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                                <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>">
                            <?php else: ?>
                                <div class="avatar-placeholder">
                                    <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="user-info">
                            <h3><?= esc($user['name']) ?></h3>
                            <p class="user-code"><?= esc($user['user_code']) ?></p>
                            <p class="user-email"><?= esc($user['email']) ?></p>
                        </div>
                    </div>

                    <div class="user-details">
                        <div class="detail-row">
                            <span class="label">Username:</span>
                            <span class="value"><?= esc($user['username']) ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Role:</span>
                            <span class="value">
                                <span class="role-badge role-<?= $user['role'] ?>">
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value">
                                <span class="status-badge status-<?= $user['is_activated'] ? 'active' : 'inactive' ?>">
                                    <?= $user['is_activated'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Last Login:</span>
                            <span class="value">
                                <?= $user['last_login_at'] ? date('M j, Y g:i A', strtotime($user['last_login_at'])) : 'Never' ?>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Created:</span>
                            <span class="value"><?= date('M j, Y', strtotime($user['created_at'])) ?></span>
                        </div>
                    </div>

                    <div class="user-actions">
                        <a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-sm btn-primary">
                            <i class="icon">👁️</i> View
                        </a>
                        
                        <?php 
                        $canEdit = false;
                        if ($current_user['role'] === 'admin') {
                            $canEdit = true;
                        } elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
                            $canEdit = true;
                        } elseif ($current_user['id'] == $user['id']) {
                            $canEdit = true;
                        }
                        ?>
                        
                        <?php if ($canEdit): ?>
                            <a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-sm btn-secondary">
                                <i class="icon">✏️</i> Edit
                            </a>
                        <?php endif; ?>

                        <?php if ($current_user['role'] === 'admin' && $user['id'] != $current_user['id']): ?>
                            <?php if (!$user['is_activated']): ?>
                                <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/resend-activation') ?>" style="display: inline;">
                                    <?= csrf_field() ?>
                                    <button type="submit" class="btn btn-sm btn-info" title="Resend Activation Email">
                                        <i class="icon">📧</i> Resend
                                    </button>
                                </form>
                            <?php endif; ?>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/toggle-status') ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn btn-sm <?= $user['is_activated'] ? 'btn-warning' : 'btn-success' ?>" 
                                        onclick="return confirm('Are you sure you want to <?= $user['is_activated'] ? 'deactivate' : 'activate' ?> this user?')">
                                    <i class="icon"><?= $user['is_activated'] ? '⏸️' : '▶️' ?></i> 
                                    <?= $user['is_activated'] ? 'Deactivate' : 'Activate' ?>
                                </button>
                            </form>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/reset-password') ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn btn-sm btn-warning" 
                                        onclick="return confirm('Are you sure you want to reset this user\'s password?')" 
                                        title="Reset Password">
                                    <i class="icon">🔑</i> Reset
                                </button>
                            </form>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id']) ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit" class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')" 
                                        title="Delete User">
                                    <i class="icon">🗑️</i> Delete
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($pager): ?>
        <div class="pagination-wrapper">
            <?= $pager->links() ?>
        </div>
    <?php endif; ?>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.stat-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.filters-section {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.filters-form {
    display: flex;
    gap: var(--spacing-md);
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.bulk-actions {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    border: 2px solid var(--accent-color);
}

.bulk-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.user-card {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    transition: transform 0.2s ease;
}

.user-card:hover {
    transform: translateY(-2px);
}

.user-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.user-checkbox {
    margin-right: var(--spacing-sm);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    color: white;
}

.user-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.user-code {
    margin: 0;
    font-family: monospace;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.user-email {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.user-details {
    margin-bottom: var(--spacing-md);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: var(--text-secondary);
}

.value {
    color: var(--text-primary);
}

.role-badge, .status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.role-admin {
    background: #dc3545;
    color: white;
}

.role-moderator {
    background: #fd7e14;
    color: white;
}

.role-user {
    background: #6c757d;
    color: white;
}

.status-active {
    background: #28a745;
    color: white;
}

.status-inactive {
    background: #6c757d;
    color: white;
}

.user-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-xl);
}

.empty-icon {
    font-size: 4rem;
    opacity: 0.5;
    margin-bottom: var(--spacing-md);
}

.pagination-wrapper {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
}

@media (max-width: 768px) {
    .users-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-form {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .user-actions {
        justify-content: center;
    }
}
</style>

<script>
function toggleBulkActions() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (checkboxes.length > 0) {
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    toggleBulkActions();
}

// Add selected user IDs to bulk form
document.getElementById('bulkForm')?.addEventListener('submit', function(e) {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (checkboxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one user.');
        return;
    }
    
    // Remove existing hidden inputs
    const existingInputs = this.querySelectorAll('input[name="user_ids[]"]');
    existingInputs.forEach(input => input.remove());
    
    // Add selected user IDs
    checkboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'user_ids[]';
        hiddenInput.value = checkbox.value;
        this.appendChild(hiddenInput);
    });
});
</script>
<?= $this->endSection() ?>
