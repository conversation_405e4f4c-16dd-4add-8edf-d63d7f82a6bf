<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Dakoii Portal' ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Color Palette */
            --bg-primary: #0A0E27;
            --bg-secondary: #151B3C;
            --bg-tertiary: #1E2749;
            --surface-card: rgba(30, 39, 73, 0.6);
            --surface-card-hover: rgba(30, 39, 73, 0.8);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #FF006E, #8338EC);
            --gradient-secondary: linear-gradient(45deg, #06FFA5, #00D4FF);
            --gradient-accent: linear-gradient(90deg, #FFB700, #FF006E);
            
            /* Text Colors */
            --text-primary: #FFFFFF;
            --text-secondary: #B8BCC8;
            --text-tertiary: #7C8091;
            --text-muted: #4A4E5C;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            --spacing-3xl: 4rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            
            /* Shadows */
            --shadow-glow-primary: 0 0 20px rgba(255, 0, 110, 0.3);
            --shadow-glow-secondary: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(255, 0, 110, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(131, 56, 236, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .container {
            max-width: 1440px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        /* Login Container */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: var(--spacing-xl);
        }

        .login-card {
            background: var(--surface-card);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .login-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .logo {
            text-align: center;
            margin-bottom: var(--spacing-2xl);
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--spacing-sm);
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .form-input:focus {
            outline: none;
            border-color: #00D4FF;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
            background: rgba(255, 255, 255, 0.08);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        .btn {
            display: inline-block;
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-primary);
            width: 100%;
            margin-top: var(--spacing-lg);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-glow-primary);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .forgot-password {
            text-align: center;
            margin-top: var(--spacing-lg);
        }

        .forgot-password a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: #00D4FF;
        }

        /* Alert Messages */
        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border: 1px solid;
            backdrop-filter: blur(10px);
        }

        .alert-error {
            background: rgba(255, 0, 110, 0.1);
            border-color: rgba(255, 0, 110, 0.3);
            color: #FF006E;
        }

        .alert-success {
            background: rgba(6, 255, 165, 0.1);
            border-color: rgba(6, 255, 165, 0.3);
            color: #06FFA5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                padding: var(--spacing-md);
            }
            
            .login-card {
                padding: var(--spacing-xl);
            }
            
            .logo h1 {
                font-size: 2rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--text-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Glassmorphism Effects */
        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
        }
    </style>
    <?= isset($additional_css) ? $additional_css : '' ?>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">
                <h1>DAKOII</h1>
                <p>Portal Access</p>
            </div>
            
            <?= $this->renderSection('content') ?>
        </div>
    </div>

    <?= isset($additional_js) ? $additional_js : '' ?>
</body>
</html>
