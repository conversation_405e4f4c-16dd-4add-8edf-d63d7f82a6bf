<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<ul class="nav-list">
    <li><a href="<?= base_url('dakoii/dashboard') ?>"><i class="icon">📊</i> Dashboard</a></li>
    <li><a href="<?= base_url('dakoii/organizations') ?>"><i class="icon">🏢</i> Organizations</a></li>
    <li class="active"><a href="<?= base_url('dakoii/users') ?>"><i class="icon">👥</i> System Users</a></li>
    <li><a href="<?= base_url('dakoii/government') ?>"><i class="icon">🏛️</i> Government Structure</a></li>
    <li><a href="<?= base_url('dakoii/logout') ?>"><i class="icon">🚪</i> Logout</a></li>
</ul>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-content">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-error">
            <ul>
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Edit User Form -->
    <div class="form-container">
        <div class="form-card glass-effect">
            <div class="form-header">
                <h2>Edit User Profile</h2>
                <p>Update user information and settings for <?= esc($user['name']) ?></p>
            </div>

            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/update') ?>" 
                  class="user-form" enctype="multipart/form-data">
                <?= csrf_field() ?>

                <!-- Current User Info -->
                <div class="current-info">
                    <div class="current-avatar">
                        <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                            <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>">
                        <?php else: ?>
                            <div class="avatar-placeholder">
                                <?= strtoupper(substr($user['name'], 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="current-details">
                        <h3><?= esc($user['name']) ?></h3>
                        <p>User Code: <?= esc($user['user_code']) ?></p>
                        <p>Current Role: <span class="role-badge role-<?= $user['role'] ?>"><?= ucfirst($user['role']) ?></span></p>
                    </div>
                </div>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?= old('name', $user['name']) ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?= old('email', $user['email']) ?>" required>
                        </div>
                    </div>

                    <?php if ($current_user['role'] === 'admin'): ?>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" class="form-control" 
                                   value="<?= old('username', $user['username']) ?>" required>
                            <small class="form-help">Only administrators can change usernames</small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Role and Permissions -->
                <?php 
                $canEditRole = false;
                if ($current_user['role'] === 'admin') {
                    $canEditRole = true;
                } elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
                    $canEditRole = true;
                }
                ?>

                <?php if ($canEditRole): ?>
                <div class="form-section">
                    <h3>Role and Permissions</h3>
                    
                    <div class="form-group">
                        <label for="role">System Role *</label>
                        <select id="role" name="role" class="form-control" required>
                            <?php if ($current_user['role'] === 'admin'): ?>
                                <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>Administrator</option>
                            <?php endif; ?>
                            <option value="moderator" <?= $user['role'] === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                            <option value="user" <?= $user['role'] === 'user' ? 'selected' : '' ?>>User</option>
                        </select>
                        <?php if ($current_user['role'] === 'moderator'): ?>
                            <small class="form-help">Moderators cannot promote users to administrator role</small>
                        <?php endif; ?>
                    </div>

                    <div class="role-permissions">
                        <div class="permission-preview" id="rolePermissions">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Profile Photo -->
                <div class="form-section">
                    <h3>Profile Photo</h3>
                    
                    <div class="photo-upload">
                        <div class="current-photo">
                            <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                                <img src="<?= base_url($user['id_photo_path']) ?>" alt="Current photo" id="currentPhoto">
                            <?php else: ?>
                                <div class="no-photo" id="currentPhoto">
                                    <span>No photo uploaded</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="upload-controls">
                            <input type="file" id="id_photo" name="id_photo" accept="image/*" class="file-input">
                            <label for="id_photo" class="file-label">
                                <i class="icon">📷</i> Choose New Photo
                            </label>
                            <small class="form-help">
                                Supported formats: JPG, PNG, GIF. Maximum size: 2MB.
                                Recommended dimensions: 300x300 pixels.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <div class="notice-icon">🔒</div>
                    <div class="notice-content">
                        <h4>Security Information</h4>
                        <ul>
                            <li>Changes to user roles require administrator approval</li>
                            <li>Email changes may require re-verification</li>
                            <li>All profile changes are logged for security auditing</li>
                            <li>Users can only edit their own basic information</li>
                        </ul>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Save Changes
                    </button>
                    <a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Edit Permissions Info -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>Edit Permissions</h3>
            </div>
            <div class="info-content">
                <div class="permission-info">
                    <?php if ($current_user['role'] === 'admin'): ?>
                        <div class="permission-level admin">
                            <h4>Administrator Access</h4>
                            <p>You can edit all user information including roles and security settings.</p>
                        </div>
                    <?php elseif ($current_user['role'] === 'moderator'): ?>
                        <div class="permission-level moderator">
                            <h4>Moderator Access</h4>
                            <p>You can edit user information except for administrator accounts.</p>
                        </div>
                    <?php else: ?>
                        <div class="permission-level user">
                            <h4>Self-Edit Access</h4>
                            <p>You can only edit your own basic profile information.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="edit-restrictions">
                    <h4>Restrictions</h4>
                    <ul>
                        <?php if ($current_user['id'] == $user['id']): ?>
                            <li>Cannot change your own role</li>
                            <li>Cannot deactivate your own account</li>
                        <?php endif; ?>
                        <?php if ($current_user['role'] === 'moderator'): ?>
                            <li>Cannot edit administrator accounts</li>
                            <li>Cannot promote users to administrator</li>
                        <?php endif; ?>
                        <li>Username changes require administrator approval</li>
                        <li>Role changes are logged and audited</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.form-card, .info-card {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
}

.form-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.form-header h2 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.form-header p {
    margin: 0;
    color: var(--text-secondary);
}

.current-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.current-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
    color: white;
}

.current-details h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.current-details p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.form-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.form-help {
    margin-top: var(--spacing-xs);
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.role-permissions {
    margin-top: var(--spacing-md);
}

.permission-preview {
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.photo-upload {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.current-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
    flex-shrink: 0;
}

.current-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-photo {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
}

.upload-controls {
    flex: 1;
}

.file-input {
    display: none;
}

.file-label {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--accent-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-label:hover {
    background: var(--accent-color-hover);
}

.security-notice {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-md);
}

.notice-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.notice-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.notice-content ul {
    margin: 0;
    padding-left: var(--spacing-md);
    color: var(--text-secondary);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.info-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.info-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.permission-level {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border-left: 4px solid;
    margin-bottom: var(--spacing-md);
}

.permission-level.admin {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.permission-level.moderator {
    border-left-color: #fd7e14;
    background: rgba(253, 126, 20, 0.1);
}

.permission-level.user {
    border-left-color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
}

.permission-level h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.edit-restrictions h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.edit-restrictions ul {
    margin: 0;
    padding-left: var(--spacing-md);
    color: var(--text-secondary);
}

.role-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.role-admin {
    background: #dc3545;
    color: white;
}

.role-moderator {
    background: #fd7e14;
    color: white;
}

.role-user {
    background: #6c757d;
    color: white;
}

@media (max-width: 768px) {
    .form-container {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .photo-upload {
        flex-direction: column;
        text-align: center;
    }
    
    .form-actions {
        justify-content: stretch;
    }
    
    .form-actions .btn {
        flex: 1;
    }
}
</style>

<script>
// Role permissions preview
const rolePermissions = {
    admin: [
        '✅ Full system access and configuration',
        '✅ Manage all users and roles',
        '✅ Create, edit, and delete organizations',
        '✅ Manage government structure data',
        '✅ Access system logs and audit trails',
        '✅ Perform bulk operations',
        '✅ Reset passwords and manage security'
    ],
    moderator: [
        '✅ Manage users (except administrators)',
        '✅ Create and edit organizations',
        '✅ View government structure data',
        '✅ Access basic system reports',
        '❌ Cannot manage administrator accounts',
        '❌ Cannot access system configuration',
        '❌ Cannot perform bulk delete operations'
    ],
    user: [
        '✅ View system dashboard',
        '✅ View organization information',
        '✅ View government structure data',
        '✅ Edit own profile information',
        '❌ Cannot manage other users',
        '❌ Cannot create or edit organizations',
        '❌ Cannot access system administration'
    ]
};

function updateRolePermissions() {
    const roleSelect = document.getElementById('role');
    const permissionsDiv = document.getElementById('rolePermissions');
    
    if (roleSelect && permissionsDiv) {
        const selectedRole = roleSelect.value;
        const permissions = rolePermissions[selectedRole] || [];
        
        permissionsDiv.innerHTML = `
            <h4>${selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)} Permissions</h4>
            <ul>
                ${permissions.map(permission => `<li>${permission}</li>`).join('')}
            </ul>
        `;
    }
}

// Initialize role permissions display
document.addEventListener('DOMContentLoaded', function() {
    updateRolePermissions();
    
    const roleSelect = document.getElementById('role');
    if (roleSelect) {
        roleSelect.addEventListener('change', updateRolePermissions);
    }
});

// Photo preview
document.getElementById('id_photo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const currentPhoto = document.getElementById('currentPhoto');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            currentPhoto.innerHTML = `<img src="${e.target.result}" alt="New photo preview">`;
        };
        reader.readAsDataURL(file);
    }
});

// Form validation
document.querySelector('.user-form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (name.length < 2) {
        e.preventDefault();
        alert('Name must be at least 2 characters long.');
        return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>
