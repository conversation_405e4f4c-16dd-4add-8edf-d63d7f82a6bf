<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Test routes
$routes->get('test', 'TestController::index');
$routes->get('login-test', 'TestController::loginTest');
$routes->get('db-test', 'TestController::dbTest');

// Dakoii Portal Authentication Routes
$routes->group('dakoii', function($routes) {
    // Login routes
    $routes->get('/', 'DakoiiAuthController::showLoginForm');
    $routes->post('authenticate', 'DakoiiAuthController::authenticateUser');
    $routes->get('logout', 'DakoiiAuthController::logoutUser');

    // Password reset routes
    $routes->get('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->post('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->get('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');
    $routes->post('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');

    // Dashboard and admin routes (will be added later)
    $routes->get('dashboard', 'DakoiiDashboardController::index', ['filter' => 'dakoii_auth']);
    $routes->group('organizations', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiOrganizationController::listOrganisations');
        $routes->get('create', 'DakoiiOrganizationController::showCreateOrganisationForm');
        $routes->post('create', 'DakoiiOrganizationController::createOrganisation');
        $routes->get('(:num)', 'DakoiiOrganizationController::viewOrganisationProfile/$1');
        $routes->get('(:num)/edit', 'DakoiiOrganizationController::showEditOrganisationModal/$1');
        $routes->post('(:num)/update', 'DakoiiOrganizationController::updateOrganisation/$1');
        $routes->post('(:num)/toggle-status', 'DakoiiOrganizationController::toggleOrganisationStatus/$1');
        $routes->post('(:num)/license-status', 'DakoiiOrganizationController::changeOrganisationLicenseStatus/$1');
        $routes->post('(:num)/upload-images', 'DakoiiOrganizationController::uploadOrganisationImages/$1');
        $routes->delete('(:num)', 'DakoiiOrganizationController::softDeleteOrganisation/$1');

        // Organization admin routes
        $routes->get('(:num)/admins', 'DakoiiOrganizationController::listOrgAdmins/$1');
        $routes->get('(:num)/admins/create', 'DakoiiOrganizationController::showCreateAdminForm/$1');
        $routes->post('(:num)/admins/create', 'DakoiiOrganizationController::createOrgAdmin/$1');
        $routes->get('admins/(:num)', 'DakoiiOrganizationController::viewOrgAdminProfile/$1');
        $routes->get('admins/(:num)/edit', 'DakoiiOrganizationController::showEditAdminModal/$1');
        $routes->post('admins/(:num)/update', 'DakoiiOrganizationController::updateOrgAdmin/$1');
        $routes->post('admins/(:num)/toggle-status', 'DakoiiOrganizationController::toggleAdminStatus/$1');
        $routes->post('admins/(:num)/reset-password', 'DakoiiOrganizationController::resetAdminPassword/$1');
        $routes->delete('admins/(:num)', 'DakoiiOrganizationController::softDeleteOrgAdmin/$1');
    });
});
