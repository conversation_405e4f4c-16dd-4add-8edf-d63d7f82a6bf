<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<ul class="nav-list">
    <li><a href="<?= base_url('dakoii/dashboard') ?>"><i class="icon">📊</i> Dashboard</a></li>
    <li><a href="<?= base_url('dakoii/organizations') ?>"><i class="icon">🏢</i> Organizations</a></li>
    <li class="active"><a href="<?= base_url('dakoii/users') ?>"><i class="icon">👥</i> System Users</a></li>
    <li><a href="<?= base_url('dakoii/government') ?>"><i class="icon">🏛️</i> Government Structure</a></li>
    <li><a href="<?= base_url('dakoii/logout') ?>"><i class="icon">🚪</i> Logout</a></li>
</ul>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Users
</a>

<?php 
$canEdit = false;
if ($current_user['role'] === 'admin') {
    $canEdit = true;
} elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
    $canEdit = true;
} elseif ($current_user['id'] == $user['id']) {
    $canEdit = true;
}
?>

<?php if ($canEdit): ?>
<a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-primary">
    <i class="icon">✏️</i> Edit Profile
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-content">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <!-- User Profile -->
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header glass-effect">
            <div class="profile-avatar">
                <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                    <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>">
                <?php else: ?>
                    <div class="avatar-placeholder">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="profile-info">
                <h1><?= esc($user['name']) ?></h1>
                <p class="user-title">
                    <span class="role-badge role-<?= $user['role'] ?>">
                        <?= ucfirst($user['role']) ?>
                    </span>
                </p>
                <p class="user-code">User Code: <?= esc($user['user_code']) ?></p>
                <p class="user-status">
                    Status: 
                    <span class="status-badge status-<?= $user['is_activated'] ? 'active' : 'inactive' ?>">
                        <?= $user['is_activated'] ? 'Active' : 'Inactive' ?>
                    </span>
                </p>
            </div>

            <div class="profile-actions">
                <?php if ($current_user['role'] === 'admin' && $user['id'] != $current_user['id']): ?>
                    <?php if (!$user['is_activated']): ?>
                        <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/resend-activation') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn btn-info">
                                <i class="icon">📧</i> Resend Activation
                            </button>
                        </form>
                    <?php endif; ?>

                    <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/toggle-status') ?>" style="display: inline;">
                        <?= csrf_field() ?>
                        <button type="submit" class="btn <?= $user['is_activated'] ? 'btn-warning' : 'btn-success' ?>" 
                                onclick="return confirm('Are you sure you want to <?= $user['is_activated'] ? 'deactivate' : 'activate' ?> this user?')">
                            <i class="icon"><?= $user['is_activated'] ? '⏸️' : '▶️' ?></i> 
                            <?= $user['is_activated'] ? 'Deactivate' : 'Activate' ?>
                        </button>
                    </form>

                    <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/reset-password') ?>" style="display: inline;">
                        <?= csrf_field() ?>
                        <button type="submit" class="btn btn-warning" 
                                onclick="return confirm('Are you sure you want to reset this user\'s password?')">
                            <i class="icon">🔑</i> Reset Password
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="profile-content">
            <!-- Basic Information -->
            <div class="info-section glass-effect">
                <h2>Basic Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <label>Full Name</label>
                        <value><?= esc($user['name']) ?></value>
                    </div>
                    <div class="info-item">
                        <label>Username</label>
                        <value><?= esc($user['username']) ?></value>
                    </div>
                    <div class="info-item">
                        <label>Email Address</label>
                        <value><?= esc($user['email']) ?></value>
                    </div>
                    <div class="info-item">
                        <label>User Code</label>
                        <value class="code-value"><?= esc($user['user_code']) ?></value>
                    </div>
                    <div class="info-item">
                        <label>System Role</label>
                        <value>
                            <span class="role-badge role-<?= $user['role'] ?>">
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </value>
                    </div>
                    <div class="info-item">
                        <label>Account Status</label>
                        <value>
                            <span class="status-badge status-<?= $user['is_activated'] ? 'active' : 'inactive' ?>">
                                <?= $user['is_activated'] ? 'Active' : 'Inactive' ?>
                            </span>
                        </value>
                    </div>
                </div>
            </div>

            <!-- Account Activity -->
            <div class="activity-section glass-effect">
                <h2>Account Activity</h2>
                <div class="activity-grid">
                    <div class="activity-item">
                        <div class="activity-icon">🕐</div>
                        <div class="activity-content">
                            <h3>Last Login</h3>
                            <p><?= $user['last_login_at'] ? date('F j, Y \a\t g:i A', strtotime($user['last_login_at'])) : 'Never logged in' ?></p>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">📅</div>
                        <div class="activity-content">
                            <h3>Account Created</h3>
                            <p><?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?></p>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">🔄</div>
                        <div class="activity-content">
                            <h3>Last Updated</h3>
                            <p><?= $user['updated_at'] ? date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) : 'Never updated' ?></p>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">✅</div>
                        <div class="activity-content">
                            <h3>Activation Status</h3>
                            <p><?= $user['is_activated'] ? 'Account is activated and ready to use' : 'Account pending activation' ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Permissions -->
            <div class="permissions-section glass-effect">
                <h2>Role Permissions</h2>
                <div class="permissions-content">
                    <?php if ($user['role'] === 'admin'): ?>
                        <div class="permission-level admin">
                            <h3>Administrator Permissions</h3>
                            <ul>
                                <li>✅ Full system access and configuration</li>
                                <li>✅ Manage all users and roles</li>
                                <li>✅ Create, edit, and delete organizations</li>
                                <li>✅ Manage government structure data</li>
                                <li>✅ Access system logs and audit trails</li>
                                <li>✅ Perform bulk operations</li>
                                <li>✅ Reset passwords and manage security</li>
                            </ul>
                        </div>
                    <?php elseif ($user['role'] === 'moderator'): ?>
                        <div class="permission-level moderator">
                            <h3>Moderator Permissions</h3>
                            <ul>
                                <li>✅ Manage users (except administrators)</li>
                                <li>✅ Create and edit organizations</li>
                                <li>✅ View government structure data</li>
                                <li>✅ Access basic system reports</li>
                                <li>❌ Cannot manage administrator accounts</li>
                                <li>❌ Cannot access system configuration</li>
                                <li>❌ Cannot perform bulk delete operations</li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="permission-level user">
                            <h3>User Permissions</h3>
                            <ul>
                                <li>✅ View system dashboard</li>
                                <li>✅ View organization information</li>
                                <li>✅ View government structure data</li>
                                <li>✅ Edit own profile information</li>
                                <li>❌ Cannot manage other users</li>
                                <li>❌ Cannot create or edit organizations</li>
                                <li>❌ Cannot access system administration</li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Security Information -->
            <div class="security-section glass-effect">
                <h2>Security Information</h2>
                <div class="security-grid">
                    <div class="security-item">
                        <div class="security-icon">🔐</div>
                        <div class="security-content">
                            <h3>Password Security</h3>
                            <p>Password is encrypted using Argon2ID hashing algorithm</p>
                        </div>
                    </div>
                    <div class="security-item">
                        <div class="security-icon">📧</div>
                        <div class="security-content">
                            <h3>Email Verification</h3>
                            <p><?= $user['is_activated'] ? 'Email address has been verified' : 'Email verification pending' ?></p>
                        </div>
                    </div>
                    <div class="security-item">
                        <div class="security-icon">🔍</div>
                        <div class="security-content">
                            <h3>Audit Trail</h3>
                            <p>All account activities are logged for security monitoring</p>
                        </div>
                    </div>
                    <div class="security-item">
                        <div class="security-icon">⏰</div>
                        <div class="security-content">
                            <h3>Session Management</h3>
                            <p>Sessions expire after 8 hours of inactivity</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    border: 4px solid var(--accent-color);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 3rem;
    color: white;
}

.profile-info {
    flex: 1;
}

.profile-info h1 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 2rem;
}

.user-title {
    margin: 0 0 var(--spacing-xs) 0;
}

.user-code, .user-status {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.profile-content {
    display: grid;
    gap: var(--spacing-lg);
}

.info-section, .activity-section, .permissions-section, .security-section {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
}

.info-section h2, .activity-section h2, .permissions-section h2, .security-section h2 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-item value {
    color: var(--text-primary);
    font-size: 1rem;
}

.code-value {
    font-family: monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
}

.activity-grid, .security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.activity-item, .security-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
}

.activity-icon, .security-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.activity-content h3, .security-content h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.activity-content p, .security-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.permission-level {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.permission-level.admin {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.permission-level.moderator {
    border-left-color: #fd7e14;
    background: rgba(253, 126, 20, 0.1);
}

.permission-level.user {
    border-left-color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
}

.permission-level h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
}

.permission-level ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.permission-level li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.role-badge, .status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.role-admin {
    background: #dc3545;
    color: white;
}

.role-moderator {
    background: #fd7e14;
    color: white;
}

.role-user {
    background: #6c757d;
    color: white;
}

.status-active {
    background: #28a745;
    color: white;
}

.status-inactive {
    background: #6c757d;
    color: white;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-actions {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .info-grid, .activity-grid, .security-grid {
        grid-template-columns: 1fr;
    }
}
</style>
<?= $this->endSection() ?>
