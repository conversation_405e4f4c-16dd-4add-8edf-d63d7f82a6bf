<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<ul class="nav-list">
    <li><a href="<?= base_url('dakoii/dashboard') ?>"><i class="icon">📊</i> Dashboard</a></li>
    <li><a href="<?= base_url('dakoii/organizations') ?>"><i class="icon">🏢</i> Organizations</a></li>
    <li class="active"><a href="<?= base_url('dakoii/users') ?>"><i class="icon">👥</i> System Users</a></li>
    <li><a href="<?= base_url('dakoii/government') ?>"><i class="icon">🏛️</i> Government Structure</a></li>
    <li><a href="<?= base_url('dakoii/logout') ?>"><i class="icon">🚪</i> Logout</a></li>
</ul>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-content">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-error">
            <ul>
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Create User Form -->
    <div class="form-container">
        <div class="form-card glass-effect">
            <div class="form-header">
                <h2>Create New System User</h2>
                <p>Create a new administrator account for the Dakoii Portal system.</p>
            </div>

            <form method="POST" action="<?= base_url('dakoii/users/create') ?>" class="user-form">
                <?= csrf_field() ?>

                <!-- Step 1: Basic Information -->
                <div class="form-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?= old('name') ?>" required>
                            <small class="form-help">Enter the user's full display name</small>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?= old('email') ?>" required>
                            <small class="form-help">This will be used for login and notifications</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" class="form-control" 
                                   value="<?= old('username') ?>" required>
                            <small class="form-help">Unique username for login (3-50 characters)</small>
                        </div>

                        <div class="form-group">
                            <label for="role">System Role *</label>
                            <select id="role" name="role" class="form-control" required>
                                <option value="">Select Role</option>
                                <?php if ($current_user['role'] === 'admin'): ?>
                                    <option value="admin" <?= old('role') === 'admin' ? 'selected' : '' ?>>Administrator</option>
                                <?php endif; ?>
                                <option value="moderator" <?= old('role') === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                <option value="user" <?= old('role') === 'user' ? 'selected' : '' ?>>User</option>
                            </select>
                            <small class="form-help">
                                <strong>Admin:</strong> Full system access<br>
                                <strong>Moderator:</strong> Can manage users and organizations<br>
                                <strong>User:</strong> Read-only access with limited permissions
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Security Settings -->
                <div class="form-section">
                    <h3>Security Settings</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" class="form-control" required>
                            <small class="form-help">Minimum 8 characters, mix of letters, numbers, and symbols recommended</small>
                        </div>

                        <div class="form-group">
                            <label for="password_confirm">Confirm Password *</label>
                            <input type="password" id="password_confirm" name="password_confirm" class="form-control" required>
                            <small class="form-help">Re-enter the password to confirm</small>
                        </div>
                    </div>

                    <div class="security-notice">
                        <div class="notice-icon">🔒</div>
                        <div class="notice-content">
                            <h4>Security Notice</h4>
                            <ul>
                                <li>The user will receive an activation email before they can log in</li>
                                <li>Passwords are encrypted using Argon2ID hashing</li>
                                <li>Users can change their password after first login</li>
                                <li>Account activity is logged for security auditing</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">👤</i> Create User
                    </button>
                    <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- User Code Preview -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>User Code Generation</h3>
            </div>
            <div class="info-content">
                <p>A unique user code will be automatically generated for this user:</p>
                <ul>
                    <li>10-12 alphanumeric characters</li>
                    <li>Globally unique across all system users</li>
                    <li>Used for internal identification and auditing</li>
                    <li>Cannot be changed after creation</li>
                </ul>
                <div class="code-preview">
                    <span class="code-label">Example:</span>
                    <span class="code-example">ABC123XYZ789</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.form-card, .info-card {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
}

.form-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.form-header h2 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.form-header p {
    margin: 0;
    color: var(--text-secondary);
}

.form-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.form-help {
    margin-top: var(--spacing-xs);
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.security-notice {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-md);
}

.notice-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.notice-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.notice-content ul {
    margin: 0;
    padding-left: var(--spacing-md);
    color: var(--text-secondary);
}

.notice-content li {
    margin-bottom: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.info-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.info-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.info-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.info-content ul {
    margin: 0 0 var(--spacing-md) 0;
    padding-left: var(--spacing-md);
    color: var(--text-secondary);
}

.info-content li {
    margin-bottom: var(--spacing-xs);
}

.code-preview {
    padding: var(--spacing-md);
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.code-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.code-example {
    display: block;
    font-family: monospace;
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--accent-color);
    margin-top: var(--spacing-xs);
}

@media (max-width: 768px) {
    .form-container {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        justify-content: stretch;
    }
    
    .form-actions .btn {
        flex: 1;
    }
}

/* Password strength indicator */
.password-strength {
    margin-top: var(--spacing-xs);
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
    width: 0%;
}

.strength-weak { background: #dc3545; }
.strength-fair { background: #fd7e14; }
.strength-good { background: #ffc107; }
.strength-strong { background: #28a745; }
</style>

<script>
// Password strength checker
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    
    // Remove existing strength indicator
    const existingIndicator = this.parentNode.querySelector('.password-strength');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    
    // Add new strength indicator
    if (password.length > 0) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';
        
        const bar = document.createElement('div');
        bar.className = `password-strength-bar strength-${strength.level}`;
        bar.style.width = strength.percentage + '%';
        
        indicator.appendChild(bar);
        this.parentNode.appendChild(indicator);
    }
});

function calculatePasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score += 25;
    if (password.length >= 12) score += 25;
    if (/[a-z]/.test(password)) score += 10;
    if (/[A-Z]/.test(password)) score += 10;
    if (/[0-9]/.test(password)) score += 10;
    if (/[^A-Za-z0-9]/.test(password)) score += 20;
    
    let level = 'weak';
    if (score >= 70) level = 'strong';
    else if (score >= 50) level = 'good';
    else if (score >= 30) level = 'fair';
    
    return { percentage: Math.min(score, 100), level: level };
}

// Username availability checker (basic client-side validation)
document.getElementById('username').addEventListener('blur', function() {
    const username = this.value.trim();
    if (username.length >= 3) {
        // Add visual feedback for valid format
        this.style.borderColor = 'var(--success-color)';
    }
});

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (emailRegex.test(email)) {
        this.style.borderColor = 'var(--success-color)';
    } else if (email.length > 0) {
        this.style.borderColor = 'var(--error-color)';
    }
});

// Password confirmation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirm = this.value;
    
    if (confirm.length > 0) {
        if (password === confirm) {
            this.style.borderColor = 'var(--success-color)';
        } else {
            this.style.borderColor = 'var(--error-color)';
        }
    }
});

// Form validation before submit
document.querySelector('.user-form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirm = document.getElementById('password_confirm').value;
    
    if (password !== confirm) {
        e.preventDefault();
        alert('Passwords do not match. Please check and try again.');
        return false;
    }
    
    if (password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>
